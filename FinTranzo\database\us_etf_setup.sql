-- =====================================================
-- 美國ETF數據庫設置
-- =====================================================

-- 1. 為 us_stocks 表添加 ETF 標識字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'us_stocks' AND column_name = 'is_etf'
    ) THEN
        ALTER TABLE us_stocks ADD COLUMN is_etf BOOLEAN DEFAULT false;
    END IF;
END $$;

-- 2. 為 us_stocks 表添加資產類型字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'us_stocks' AND column_name = 'asset_type'
    ) THEN
        ALTER TABLE us_stocks ADD COLUMN asset_type VARCHAR(20) DEFAULT 'STOCK';
    END IF;
END $$;

-- 3. 創建 ETF 專用視圖
CREATE OR REPLACE VIEW us_etf_view AS
SELECT 
    symbol,
    name,
    sector,
    price,
    change_amount,
    change_percent,
    volume,
    market_cap,
    price_date,
    updated_at
FROM us_stocks
WHERE is_etf = true
ORDER BY market_cap DESC NULLS LAST;

-- 4. 創建 ETF 搜索函數
CREATE OR REPLACE FUNCTION search_us_etf(
    search_term TEXT,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        s.is_etf = true
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
    ORDER BY s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 5. 創建批量插入 ETF 函數
CREATE OR REPLACE FUNCTION insert_us_etf_batch(
    etf_data JSONB
)
RETURNS INTEGER AS $$
DECLARE
    etf_record JSONB;
    insert_count INTEGER := 0;
BEGIN
    FOR etf_record IN SELECT * FROM jsonb_array_elements(etf_data)
    LOOP
        INSERT INTO us_stocks (
            symbol, 
            name, 
            is_etf, 
            asset_type,
            sector,
            is_sp500,
            created_at,
            updated_at
        ) VALUES (
            etf_record->>'symbol',
            etf_record->>'name',
            true,
            'ETF',
            COALESCE(etf_record->>'sector', 'ETF'),
            false,  -- ETF 不屬於 S&P 500
            NOW(),
            NOW()
        )
        ON CONFLICT (symbol) 
        DO UPDATE SET
            name = EXCLUDED.name,
            is_etf = true,
            asset_type = 'ETF',
            sector = COALESCE(EXCLUDED.sector, 'ETF'),
            updated_at = NOW();
        
        insert_count := insert_count + 1;
    END LOOP;
    
    RETURN insert_count;
END;
$$ LANGUAGE plpgsql;

-- 6. 創建索引以提高查詢性能
CREATE INDEX IF NOT EXISTS idx_us_stocks_is_etf ON us_stocks(is_etf);
CREATE INDEX IF NOT EXISTS idx_us_stocks_asset_type ON us_stocks(asset_type);
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol_etf ON us_stocks(symbol) WHERE is_etf = true;

-- 7. 創建統計函數
CREATE OR REPLACE FUNCTION get_us_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    stock_count BIGINT,
    etf_count BIGINT,
    sp500_count BIGINT,
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE is_etf = false) as stock_count,
        COUNT(*) FILTER (WHERE is_etf = true) as etf_count,
        COUNT(*) FILTER (WHERE is_sp500 = true) as sp500_count,
        MAX(updated_at) as last_updated
    FROM us_stocks;
END;
$$ LANGUAGE plpgsql;

-- 8. 創建 ETF 分類視圖
CREATE OR REPLACE VIEW us_etf_by_sector AS
SELECT 
    sector,
    COUNT(*) as etf_count,
    AVG(price) as avg_price,
    SUM(market_cap) as total_market_cap
FROM us_stocks
WHERE is_etf = true AND price IS NOT NULL
GROUP BY sector
ORDER BY etf_count DESC;

-- 9. 權限設置
GRANT SELECT ON us_etf_view TO anon, authenticated;
GRANT SELECT ON us_etf_by_sector TO anon, authenticated;
GRANT EXECUTE ON FUNCTION search_us_etf(TEXT, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_us_stock_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION insert_us_etf_batch(JSONB) TO authenticated;

-- 10. 創建 RLS 政策（如果需要）
-- ALTER TABLE us_stocks ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "ETF data is publicly readable" ON us_stocks FOR SELECT USING (is_etf = true);
