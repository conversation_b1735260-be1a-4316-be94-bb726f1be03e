import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Animated,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { DeviceMotion } from 'expo-sensors';
import { Calendar } from 'react-native-calendars';
import AddTransactionModal from '../../components/AddTransactionModal';
import SwipeableTransactionItem from '../../components/SwipeableTransactionItem';
import DatePickerModal from '../../components/DatePickerModal';
import { recurringTransactionService } from '../../services/recurringTransactionService';
import { assetTransactionSyncService } from '../../services/assetTransactionSyncService';
import { transactionDataService, Transaction } from '../../services/transactionDataService';
import { RecurringFrequency } from '../../types';
import { eventEmitter, EVENTS } from '../../services/eventEmitter';

export default function TransactionsScreen() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date().toISOString().split('T')[0]);

  // 動畫相關
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;

  // 監控 currentMonth 變化
  useEffect(() => {
    console.log('Current month state changed to:', currentMonth);
  }, [currentMonth]);

  // 設置搖動檢測
  useEffect(() => {
    let subscription: any;

    const setupShakeDetection = async () => {
      // 檢查設備運動傳感器是否可用
      const isAvailable = await DeviceMotion.isAvailableAsync();
      if (!isAvailable) {
        console.log('Device motion is not available');
        return;
      }

      // 設置更新間隔
      DeviceMotion.setUpdateInterval(100);

      // 訂閱設備運動事件
      subscription = DeviceMotion.addListener((motionData) => {
        const { acceleration } = motionData;
        if (acceleration) {
          const { x, y, z } = acceleration;

          // 計算總加速度
          const totalAcceleration = Math.sqrt(x * x + y * y + z * z);

          // 搖動閾值（降低敏感度，只在記帳頁面生效）
          const shakeThreshold = 3.5;

          if (totalAcceleration > shakeThreshold) {
            const now = Date.now();
            // 防抖：至少間隔500ms才能觸發下一次搖動檢測（降低敏感度）
            if (now - lastShakeDetectionTime.current > 500) {
              lastShakeDetectionTime.current = now;
              handleShake();
            }
          }
        }
      });
    };

    setupShakeDetection();

    // 清理函數
    return () => {
      if (subscription) {
        subscription.remove();
      }
      if (shakeTimeoutRef.current) {
        clearTimeout(shakeTimeoutRef.current);
      }
    };
  }, [shakeCount]); // 依賴 shakeCount 以確保 handleShake 中的狀態是最新的

  // 搖動檢測相關
  const [shakeCount, setShakeCount] = useState(0);
  const shakeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastShakeTime = useRef(0);
  const lastShakeDetectionTime = useRef(0);

  // 回到當前月份的函數
  const goToCurrentMonth = () => {
    const today = new Date().toISOString().split('T')[0];
    console.log('Shake detected: Going to current month:', today);

    // 觸覺反饋
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    setCurrentMonth(today);
    setSelectedDate(today);

    // 播放翻頁動畫
    playPageFlipAnimation();
  };

  // 搖動檢測邏輯
  const handleShake = () => {
    // 檢查是否已經在當月，如果是則取消搖動檢測
    const today = new Date();
    const currentYearMonth = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}`;
    const selectedYearMonth = currentMonth.substring(0, 7); // 取 YYYY-MM 部分

    if (currentYearMonth === selectedYearMonth) {
      console.log('Already in current month, ignoring shake detection');
      return;
    }

    const now = Date.now();
    const timeDiff = now - lastShakeTime.current;

    console.log('Shake detected, time diff:', timeDiff, 'current count:', shakeCount);

    // 如果距離上次搖動超過1.5秒，重置計數
    if (timeDiff > 1500) {
      console.log('Resetting shake count');
      setShakeCount(1);
      lastShakeTime.current = now;

      // 設置超時重置計數
      if (shakeTimeoutRef.current) {
        clearTimeout(shakeTimeoutRef.current);
      }
      shakeTimeoutRef.current = setTimeout(() => {
        console.log('Timeout: Resetting shake count');
        setShakeCount(0);
      }, 1500);
    } else {
      // 在短時間內的第二次搖動
      console.log('Second shake detected!');
      setShakeCount(0);
      goToCurrentMonth();

      // 清除超時
      if (shakeTimeoutRef.current) {
        clearTimeout(shakeTimeoutRef.current);
      }
    }

    lastShakeTime.current = now;
  };
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [futureRecurringTransactions, setFutureRecurringTransactions] = useState<any[]>([]);

  // 初始化交易資料服務和處理循環交易的生成
  useEffect(() => {
    // 直接獲取已初始化的交易資料
    setTransactions(transactionDataService.getTransactions());

    // 添加監聽器來同步資料
    const handleTransactionsUpdate = () => {
      setTransactions(transactionDataService.getTransactions());
    };
    transactionDataService.addListener(handleTransactionsUpdate);

    const processRecurringTransactions = () => {
      const newTransactions = recurringTransactionService.processRecurringTransactions();
      if (newTransactions.length > 0) {
        // 將新的循環交易添加到服務中
        newTransactions.forEach(async (transaction) => {
          await transactionDataService.addTransaction(transaction);
        });
      }
    };

    const updateFutureRecurringTransactions = () => {
      console.log('🔄 更新未來循環交易...');
      const futureTransactions = recurringTransactionService.generateFutureRecurringTransactions(12);
      console.log('📊 未來循環交易數量:', futureTransactions.length);
      setFutureRecurringTransactions(futureTransactions);
    };

    // 監聽循環交易創建事件
    const handleRecurringTransactionCreated = (data: any) => {
      console.log('📡 收到循環交易創建事件:', data);
      console.log('🔄 強制刷新未來循環交易...');
      updateFutureRecurringTransactions();
    };

    // 🔥 方法9：TransactionsScreen 增強的負債添加事件監聽器
    const handleLiabilityAdded = (liability: any) => {
      console.log('🔥 方法9 - TransactionsScreen 收到負債添加事件:', liability.name);
      console.log('🔥 方法9 - 立即刷新交易數據');

      // 立即刷新
      setTransactions(transactionDataService.getTransactions());
      updateFutureRecurringTransactions();

      // 延遲再次刷新
      setTimeout(() => {
        console.log('🔥 方法9 - 延遲刷新交易數據');
        setTransactions(transactionDataService.getTransactions());
        updateFutureRecurringTransactions();
      }, 500);
    };

    const handleForceRefreshAll = (data: any) => {
      console.log('🔥 方法9 - TransactionsScreen 收到強制刷新事件:', data);
      console.log('🔥 方法9 - 立即刷新交易數據');

      setTransactions(transactionDataService.getTransactions());
      updateFutureRecurringTransactions();

      // 延遲再次刷新
      setTimeout(() => {
        console.log('🔥 方法9 - 延遲刷新交易數據');
        setTransactions(transactionDataService.getTransactions());
        updateFutureRecurringTransactions();
      }, 300);
    };

    eventEmitter.on(EVENTS.RECURRING_TRANSACTION_CREATED, handleRecurringTransactionCreated);
    eventEmitter.on(EVENTS.LIABILITY_ADDED, handleLiabilityAdded);
    eventEmitter.on(EVENTS.LIABILITY_DELETED, handleLiabilityAdded); // 🔥 修復4：負債刪除也需要刷新
    eventEmitter.on(EVENTS.FORCE_REFRESH_ALL, handleForceRefreshAll);
    eventEmitter.on(EVENTS.FORCE_REFRESH_TRANSACTIONS, handleForceRefreshAll);

    // 確保基本資產存在
    assetTransactionSyncService.ensureBasicAssets();

    // 開發模式下添加測試資料
    if (__DEV__) {
      const currentTransactions = transactionDataService.getTransactions();
      if (currentTransactions.length === 0) {
        console.log('🧪 添加月曆測試交易資料...');
        const testTransactions = [
          {
            amount: 5000,
            type: 'expense' as const,
            description: '餐飲',
            category: '餐飲',
            account: '現金',
            date: new Date().toISOString(),
          },
          {
            amount: 30000,
            type: 'income' as const,
            description: '薪水',
            category: '薪水',
            account: '銀行',
            date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 昨天
          },
        ];

        testTransactions.forEach(transaction => {
          transactionDataService.addTransaction(transaction);
        });

        console.log('✅ 月曆測試資料已添加');
      }
    }

    // 每天檢查一次循環交易
    processRecurringTransactions();
    // 更新未來的循環交易
    updateFutureRecurringTransactions();

    // 設定定時器，每天午夜檢查
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const timeUntilMidnight = tomorrow.getTime() - now.getTime();

    const timeoutId = setTimeout(() => {
      processRecurringTransactions();
      updateFutureRecurringTransactions();

      // 設定每24小時執行一次
      const intervalId = setInterval(() => {
        processRecurringTransactions();
        updateFutureRecurringTransactions();
      }, 24 * 60 * 60 * 1000);

      return () => clearInterval(intervalId);
    }, timeUntilMidnight);

    return () => {
      clearTimeout(timeoutId);
      transactionDataService.removeListener(handleTransactionsUpdate);
      eventEmitter.off(EVENTS.RECURRING_TRANSACTION_CREATED, handleRecurringTransactionCreated);
      eventEmitter.off(EVENTS.LIABILITY_ADDED, handleLiabilityAdded);
      eventEmitter.off(EVENTS.LIABILITY_DELETED, handleLiabilityAdded); // 🔥 修復4：清理負債刪除監聽器
      eventEmitter.off(EVENTS.FORCE_REFRESH_ALL, handleForceRefreshAll);
      eventEmitter.off(EVENTS.FORCE_REFRESH_TRANSACTIONS, handleForceRefreshAll);
    };
  }, []);

  const handleEditTransaction = (transaction: Transaction) => {
    setEditingTransaction(transaction);
    setShowAddModal(true);
  };

  const handleUpdateTransaction = async (updatedTransaction: any) => {
    if (editingTransaction) {
      // 更新現有交易
      await transactionDataService.updateTransaction(editingTransaction.id, updatedTransaction);
      setEditingTransaction(null);
    } else {
      // 添加新交易
      await handleAddTransaction(updatedTransaction);
    }
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setEditingTransaction(null);
  };

  const handleAddTransaction = async (newTransaction: any) => {
    // 處理交易對資產的影響
    assetTransactionSyncService.processTransaction(newTransaction);

    // 如果是循環交易，創建循環交易模板並立即生成第一筆交易
    if (newTransaction.is_recurring) {
      // 確保 startDate 是 Date 對象
      const startDate = newTransaction.start_date instanceof Date
        ? newTransaction.start_date
        : new Date(newTransaction.start_date || newTransaction.date);

      // 創建循環交易模板
      recurringTransactionService.createRecurringTransaction({
        amount: newTransaction.amount,
        type: newTransaction.type,
        description: newTransaction.description,
        category: newTransaction.category,
        account: newTransaction.account,
        frequency: newTransaction.recurring_frequency,
        maxOccurrences: newTransaction.max_occurrences,
        startDate: startDate,
      });

      // 立即生成第一筆交易記錄
      const firstTransaction = {
        ...newTransaction,
        id: `first_${Date.now()}`, // 確保ID唯一
      };
      await transactionDataService.addTransaction(firstTransaction);

      // 處理循環交易，生成後續的交易記錄（如果有到期的）
      const generatedTransactions = recurringTransactionService.processRecurringTransactions();
      if (generatedTransactions.length > 0) {
        for (const transaction of generatedTransactions) {
          await transactionDataService.addTransaction(transaction);
        }
      }

      // 更新未來的循環交易
      const futureTransactions = recurringTransactionService.generateFutureRecurringTransactions(12);
      setFutureRecurringTransactions(futureTransactions);
    } else {
      // 普通交易直接添加到服務中
      await transactionDataService.addTransaction(newTransaction);
    }
  };

  const handleDeleteTransaction = async (item: any, deleteType?: 'single' | 'future' | 'all') => {
    if (item.is_recurring && deleteType) {
      switch (deleteType) {
        case 'single':
          // 單次刪除：只刪除這一筆交易記錄
          assetTransactionSyncService.reverseTransaction(item);
          await transactionDataService.deleteTransaction(item.id);
          setFutureRecurringTransactions(prev => prev.filter(t => t.id !== item.id));
          break;

        case 'future':
          // 向後刪除：刪除包含這個月之後的所有相關交易
          const itemDate = new Date(item.date);
          const itemMonth = itemDate.getFullYear() * 12 + itemDate.getMonth();

          // 找到對應的循環交易模板
          const recurringTemplate = recurringTransactionService.getRecurringTransactions()
            .find(rt => rt.description === item.description && rt.amount === item.amount);

          if (recurringTemplate) {
            // 停用循環交易模板
            recurringTransactionService.deactivateRecurringTransaction(recurringTemplate.id);

            // 刪除當前月份及之後的所有相關交易
            const currentTransactions = transactionDataService.getTransactions();
            for (const t of currentTransactions) {
              if (t.description === item.description && t.amount === item.amount) {
                const tDate = new Date(t.date);
                const tMonth = tDate.getFullYear() * 12 + tDate.getMonth();
                if (tMonth >= itemMonth) {
                  await transactionDataService.deleteTransaction(t.id);
                }
              }
            }

            // 刪除未來的相關交易
            setFutureRecurringTransactions(prev => prev.filter(t => {
              if (t.description !== item.description || t.amount !== item.amount) return true;
              const tDate = new Date(t.date);
              const tMonth = tDate.getFullYear() * 12 + tDate.getMonth();
              return tMonth < itemMonth;
            }));
          }
          break;

        case 'all':
          // 全部刪除：刪除所有相關的交易記錄和循環交易模板
          const allRecurringTemplate = recurringTransactionService.getRecurringTransactions()
            .find(rt => rt.description === item.description && rt.amount === item.amount);

          if (allRecurringTemplate) {
            // 刪除循環交易模板
            recurringTransactionService.deleteRecurringTransaction(allRecurringTemplate.id);

            // 刪除所有相關的交易記錄
            const allTransactions = transactionDataService.getTransactions();
            for (const t of allTransactions) {
              if (t.description === item.description && t.amount === item.amount && t.is_recurring) {
                await transactionDataService.deleteTransaction(t.id);
              }
            }

            // 刪除所有相關的未來交易
            setFutureRecurringTransactions(prev => prev.filter(t =>
              !(t.description === item.description && t.amount === item.amount)
            ));
          }
          break;
      }
    } else {
      // 普通交易直接刪除
      assetTransactionSyncService.reverseTransaction(item);
      await transactionDataService.deleteTransaction(item.id);
    }
  };

  const mockAccounts = transactionDataService.getAccounts();
  const mockCategories = transactionDataService.getCategories();

  const getTransactionsForDate = (date: string) => {
    // 合併實際交易和未來的循環交易，但避免重複
    const actualTransactions = transactions.filter(t => t.date.split('T')[0] === date);
    const futureTransactions = futureRecurringTransactions.filter(t => t.date.split('T')[0] === date);

    // 去重：如果實際交易中已經有相同的循環交易記錄，就不包含未來交易
    const filteredFutureTransactions = futureTransactions.filter(ft =>
      !actualTransactions.some(at =>
        at.description === ft.description &&
        at.amount === ft.amount &&
        at.is_recurring
      )
    );

    return [...actualTransactions, ...filteredFutureTransactions];
  };

  const getDayTransactionSummary = (date: string) => {
    const dayTransactions = getTransactionsForDate(date);
    const income = dayTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    const expense = dayTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    return { income, expense, count: dayTransactions.length };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: 'TWD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // 格式化收支總額顯示 (簡潔版本適合月曆)
  const formatNetAmount = (amount: number) => {
    if (amount === 0) return '';
    const absAmount = Math.abs(amount);

    // 簡化顯示：超過萬元顯示萬，否則顯示完整數字
    let formattedAmount: string;
    if (absAmount >= 10000) {
      const wanAmount = Math.round(absAmount / 1000) / 10; // 保留一位小數
      formattedAmount = wanAmount % 1 === 0 ? `${Math.round(wanAmount)}萬` : `${wanAmount}萬`;
    } else {
      formattedAmount = new Intl.NumberFormat('zh-TW', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(absAmount);
    }

    return amount > 0 ? `+${formattedAmount}` : `-${formattedAmount}`;
  };

  // 自定義日期組件 - 在日期下方顯示收支總額
  const renderDay = (day: any, item: any) => {
    if (!day) return <View style={styles.emptyDay} />;

    const dateString = day.dateString;
    const dayNumber = day.day;
    const isSelected = dateString === selectedDate;
    const isToday = dateString === new Date().toISOString().split('T')[0];
    const isDisabled = day.state === 'disabled';

    // 獲取當日交易摘要
    const summary = getDayTransactionSummary(dateString);
    const netAmount = summary.income - summary.expense;
    const hasTransactions = summary.count > 0;

    return (
      <TouchableOpacity
        style={[
          styles.dayContainer,
          isSelected && styles.selectedDayContainer,
          isToday && !isSelected && styles.todayContainer,
        ]}
        onPress={() => setSelectedDate(dateString)}
        disabled={isDisabled}
      >
        <Text style={[
          styles.dayText,
          isSelected && styles.selectedDayText,
          isToday && !isSelected && styles.todayText,
          isDisabled && styles.disabledDayText,
        ]}>
          {dayNumber}
        </Text>
        {hasTransactions && (
          <Text style={[
            styles.amountText,
            isSelected && styles.selectedAmountText,
            netAmount > 0 ? styles.positiveAmount : styles.negativeAmount,
          ]}>
            {formatNetAmount(netAmount)}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  // 翻頁動畫效果
  const playPageFlipAnimation = () => {
    // 觸覺反饋
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // 視覺動畫序列
    Animated.sequence([
      // 1. 輕微縮放和淡出
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0.98,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.8,
          duration: 100,
          useNativeDriver: true,
        }),
      ]),
      // 2. 恢復正常
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 300,
          friction: 10,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // 滑動效果
    Animated.sequence([
      Animated.timing(slideAnim, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 300,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleMonthChange = (month: any) => {
    console.log('Month changed to:', month.dateString);

    // 播放翻頁動畫
    playPageFlipAnimation();

    // 更新月份
    setCurrentMonth(month.dateString);
  };

  const handleDatePickerSelect = (year: number, month: number) => {
    // 播放選擇反饋
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // 創建新的日期字符串，確保格式正確
    const monthStr = month.toString().padStart(2, '0');
    const dateString = `${year}-${monthStr}-01`;

    console.log('Jumping to date:', dateString); // 調試日誌

    // 先關閉模態框
    setShowDatePicker(false);

    // 使用 setTimeout 確保狀態更新順序
    setTimeout(() => {
      setCurrentMonth(dateString);
      setSelectedDate(dateString);

      // 播放翻頁動畫
      playPageFlipAnimation();
    }, 100);
  };

  const renderTransactionItem = ({ item }: { item: any }) => {
    // 修復帳戶顯示邏輯：直接使用交易記錄中的account字段
    const account = { name: item.account };
    const category = transactionDataService.getCategoryByName(item.category);
    const isFutureTransaction = futureRecurringTransactions.some(ft => ft.id === item.id);

    return (
      <SwipeableTransactionItem
        item={item}
        account={account}
        category={category}
        isFutureTransaction={isFutureTransaction}
        onDelete={handleDeleteTransaction}
        onEdit={handleEditTransaction}
        formatCurrency={formatCurrency}
      />
    );
  };

  const markedDates = [...transactions, ...futureRecurringTransactions].reduce((acc, transaction) => {
    const date = transaction.date.split('T')[0];

    // 避免重複處理同一日期
    if (acc[date]) return acc;

    const summary = getDayTransactionSummary(date);
    const netAmount = summary.income - summary.expense;

    acc[date] = {
      marked: true,
      dotColor: summary.income > summary.expense ? '#34C759' : '#FF3B30',
      // 移除 customStyles，因為我們使用自定義日期組件
    };
    return acc;
  }, {} as any);

  // 確保選中的日期有標記（自定義組件會處理選中狀態）
  if (selectedDate && !markedDates[selectedDate]) {
    markedDates[selectedDate] = {
      marked: false, // 沒有交易記錄
    };
  }

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />



      {/* Content */}
      <ScrollView style={styles.content}>
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim },
                { translateX: slideAnim }
              ]
            }}
          >
            <Calendar
              key={currentMonth} // 強制重新渲染
              current={currentMonth}
              onDayPress={(day) => setSelectedDate(day.dateString)}
              onMonthChange={handleMonthChange}
              markedDates={markedDates}
              enableSwipeMonths={true}
              dayComponent={renderDay}
              theme={{
                backgroundColor: '#ffffff',
                calendarBackground: '#ffffff',
                textSectionTitleColor: '#b6c1cd',
                selectedDayBackgroundColor: '#007AFF',
                selectedDayTextColor: '#ffffff',
                todayTextColor: '#007AFF',
                dayTextColor: '#2d4150',
                textDisabledColor: '#d9e1e8',
                dotColor: '#00adf5',
                selectedDotColor: '#ffffff',
                arrowColor: '#007AFF',
                disabledArrowColor: '#d9e1e8',
                monthTextColor: '#2d4150',
                indicatorColor: '#007AFF',
                textDayFontWeight: '500',
                textMonthFontWeight: 'bold',
                textDayHeaderFontWeight: '600',
                textDayFontSize: 16,
                textMonthFontSize: 18,
                textDayHeaderFontSize: 14,
              }}
              renderHeader={(date) => (
                <TouchableOpacity
                  style={styles.customHeader}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    setShowDatePicker(true);
                  }}
                >
                  <Text style={styles.headerText}>
                    {new Date(date).toLocaleDateString('zh-TW', {
                      year: 'numeric',
                      month: 'long'
                    })}
                  </Text>
                  <Ionicons name="chevron-down" size={16} color="#2d4150" />
                </TouchableOpacity>
              )}
            />
          </Animated.View>

          {/* Selected Date Transactions */}
          <View style={styles.selectedDateSection}>
            <View style={styles.dateHeader}>
              <Text style={styles.selectedDateTitle}>
                {new Date(selectedDate).toLocaleDateString('zh-TW', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long',
                })}
              </Text>
              <TouchableOpacity
                style={styles.addButton}
                onPress={() => setShowAddModal(true)}
              >
                <Ionicons name="add" size={24} color="#fff" />
              </TouchableOpacity>
            </View>

            {getTransactionsForDate(selectedDate).length > 0 ? (
              getTransactionsForDate(selectedDate).map((item) => (
                <View key={item.id}>
                  {renderTransactionItem({ item, index: 0 })}
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>此日期沒有交易記錄</Text>
            )}
          </View>
        </ScrollView>

      {/* Add Transaction Modal */}
      <AddTransactionModal
        visible={showAddModal}
        onClose={handleCloseModal}
        onAdd={handleUpdateTransaction}
        selectedDate={selectedDate}
        editingTransaction={editingTransaction}
      />

      {/* Date Picker Modal */}
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onSelect={handleDatePickerSelect}
        currentDate={currentMonth}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  dateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },

  addButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  selectedDateSection: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  selectedDateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },

  emptyText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    paddingVertical: 40,
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d4150',
    marginRight: 8,
  },
  // 自定義日期組件樣式
  emptyDay: {
    width: 32,
    height: 50,
  },
  dayContainer: {
    width: 32,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 2,
  },
  selectedDayContainer: {
    backgroundColor: '#007AFF',
    borderRadius: 16,
  },
  todayContainer: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    borderRadius: 16,
  },
  dayText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2d4150',
    textAlign: 'center',
  },
  selectedDayText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  todayText: {
    color: '#007AFF',
    fontWeight: 'bold',
  },
  disabledDayText: {
    color: '#d9e1e8',
  },
  amountText: {
    fontSize: 9,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 11,
    marginTop: 1,
  },
  selectedAmountText: {
    color: '#ffffff',
  },
  positiveAmount: {
    color: '#34C759',
  },
  negativeAmount: {
    color: '#FF3B30',
  },
});
